#!/usr/bin/env python3
"""
Intel-Optimized LSTM Stock Prediction Service
6-feature input (adj close, volume, RSI-14, <PERSON><PERSON><PERSON> %) with 75-day window / 5-day horizon
Trains up to day -5, predicts 6 days (-4, -3, -2, -1, 0, +1) with bias offset only
"""

import os
import sys
import json
from datetime import date, timedelta
from pathlib import Path

# Module-level constants - will be calculated dynamically for available data range
PRED_DATES = [date(2025, 6, 4), date(2025, 6, 5)]

# Intel optimization environment variables
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
os.environ['TF_ENABLE_BF16_CONVOLUTIONS'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Enable oneDNN verbose if debug flag is set
if os.environ.get('LSTM_DEBUG_ONEDNN') == '1':
    os.environ['ONEDNN_VERBOSE'] = '1'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# Import required libraries
import pandas as pd
import numpy as np
from pandas.tseries.offsets import BDay
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import joblib
import tensorflow as tf
from tensorflow.keras import Sequential
from tensorflow.keras.layers import Dense, LSTM, Dropout
from tensorflow.keras.callbacks import EarlyStopping

# Common Constants for Technical Indicator-based LSTM Prediction
WINDOW_DAYS = 75        # LSTM input sequence length (75 trading days)
HORIZON_DAYS = 5        # Prediction horizon (5 business days ahead)
RSI_PERIOD = 14         # RSI calculation period
BB_PERIOD = 20          # Bollinger Bands period
BB_STD_MULT = 2         # Bollinger Bands standard deviation multiplier

# Intel optimizations enabled via environment variables

# Configure TensorFlow threading for Intel Core Ultra 7 155H
tf.config.threading.set_intra_op_parallelism_threads(16)
tf.config.threading.set_inter_op_parallelism_threads(2)
tf.get_logger().setLevel('ERROR')

# Print startup log
print(f"✅  TensorFlow-Intel {tf.__version__} — oneDNN enabled", file=sys.stderr)


def compute_indicators(df):
    """
    Compute technical indicators for LSTM features

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with columns ['Date', 'close', 'volume'] (or similar)

    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    """
    # Ensure we have the required columns
    if 'Adj Close' in df.columns:
        df = df.rename(columns={'Adj Close': 'close'})
    if 'Volume' in df.columns:
        df = df.rename(columns={'Volume': 'volume'})

    # Sort by date to ensure proper calculation
    df = df.sort_values('Date').copy()

    # Calculate RSI(14)
    delta = df['close'].diff()
    gain = delta.clip(lower=0)
    loss = -delta.clip(upper=0)

    # Use exponential moving average for RSI calculation
    avg_gain = gain.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    avg_loss = loss.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    # Normalize RSI from 0-100 range to 0-1 scale
    df['rsi14'] = rsi / 100.0

    # Calculate Bollinger Bands
    sma = df['close'].rolling(window=BB_PERIOD).mean()
    std = df['close'].rolling(window=BB_PERIOD).std()
    bb_upper = sma + (BB_STD_MULT * std)
    bb_lower = sma - (BB_STD_MULT * std)

    # Calculate Bollinger Band features as percentages
    # bb_upper_pct: How far current price is above upper band (as percentage)
    df['bb_upper_pct'] = ((df['close'] - bb_upper) / bb_upper * 100).clip(lower=-100, upper=100)

    # bb_lower_pct: How far current price is above lower band (as percentage)
    df['bb_lower_pct'] = ((df['close'] - bb_lower) / bb_lower * 100).clip(lower=-100, upper=100)

    # bb_width_pct: Band width as percentage of middle line
    df['bb_width_pct'] = ((bb_upper - bb_lower) / sma * 100).clip(lower=0, upper=100)

    # Remove NaN values
    df = df.dropna()

    # Debug logging for technical indicator statistics
    print(f"Technical indicator stats:", file=sys.stderr)
    print(f"  RSI14 range: {df['rsi14'].min():.3f} - {df['rsi14'].max():.3f}", file=sys.stderr)
    print(f"  BB Upper %: {df['bb_upper_pct'].min():.1f} - {df['bb_upper_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Lower %: {df['bb_lower_pct'].min():.1f} - {df['bb_lower_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Width %: {df['bb_width_pct'].min():.1f} - {df['bb_width_pct'].max():.1f}", file=sys.stderr)

    # Return DataFrame with exact column order required
    feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    return df[feature_cols]


def update_history_csv(ticker, prediction_date, is_correct):
    """
    Update traffic light history CSV (keep last 5 rows)

    Parameters:
    -----------
    ticker : str
        Stock ticker symbol
    prediction_date : date or Timestamp
        Date of the prediction
    is_correct : int
        1 if prediction was correct, 0 if incorrect
    """
    # Create traffic_history directory if it doesn't exist
    ROOT_DIR = Path(__file__).resolve().parents[1]
    history_dir = ROOT_DIR / "data" / "traffic_history"
    history_dir.mkdir(parents=True, exist_ok=True)

    # Path to ticker's history file
    history_file = history_dir / f"{ticker}.csv"

    # Convert prediction_date to date object for consistency
    if hasattr(prediction_date, 'date'):
        prediction_date = prediction_date.date()

    # Load existing history or create new DataFrame
    if history_file.exists():
        try:
            history_df = pd.read_csv(history_file)
            history_df['date'] = pd.to_datetime(history_df['date']).dt.date
        except Exception as e:
            print(f"Error reading history file {history_file}: {e}", file=sys.stderr)
            history_df = pd.DataFrame(columns=['date', 'is_correct'])
    else:
        history_df = pd.DataFrame(columns=['date', 'is_correct'])

    # Add new record
    new_record = pd.DataFrame({
        'date': [prediction_date],
        'is_correct': [is_correct]
    })
    history_df = pd.concat([history_df, new_record], ignore_index=True)

    # Keep only last 5 rows
    history_df = history_df.tail(5)

    # Save updated history
    history_df.to_csv(history_file, index=False)


def load_and_prepare_data(ticker, use_volume=False):
    """Load data and prepare for training with robust date handling for limited datasets"""
    try:
        # Load data
        ROOT_DIR = Path(__file__).resolve().parents[1]              # …/financial_dashboard
        csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

        if not csv_path.exists():
            print(f"Data file not found: {csv_path}", file=sys.stderr)
            sys.exit(1)

        # Read price data with proper data types
        df = pd.read_csv(csv_path)

        # Parse Date column - keep as Timestamp for consistent comparison
        df['Date'] = pd.to_datetime(df['Date'])

        # Convert numeric columns to float64
        for col in df.columns:
            if col != 'Date':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Check if ticker exists
        if ticker not in df.columns:
            print(f"Ticker {ticker} not found in data", file=sys.stderr)
            sys.exit(1)

        # Get available date range from dataset
        min_date = df['Date'].min()
        max_date = df['Date'].max()

        print(f"Dataset range: {min_date.date()} to {max_date.date()}", file=sys.stderr)

        # Calculate training end date (5 business days before first prediction date)
        pred_date_ts = pd.Timestamp(PRED_DATES[0])
        train_end_date = pred_date_ts - BDay(5)  # 5 business days before first prediction

        # Calculate ideal training start (3.5 years before)
        ideal_start = pred_date_ts - pd.Timedelta(days=int(3.5 * 365))

        # Use maximum available historical data (start from dataset beginning if needed)
        train_start_date = max(min_date, ideal_start)

        print(f"Training range: {train_start_date.date()} to {train_end_date.date()}", file=sys.stderr)

        # Load volume data if requested
        volume_data = None
        if use_volume:
            volume_path = ROOT_DIR / "data" / "sp500_volume_3y.csv"
            if not volume_path.exists():
                print(f"Volume data file not found: {volume_path}", file=sys.stderr)
                sys.exit(1)

            volume_df = pd.read_csv(volume_path)
            volume_df['Date'] = pd.to_datetime(volume_df['Date'])  # Keep as Timestamp

            # Convert numeric columns to float64
            for col in volume_df.columns:
                if col != 'Date':
                    volume_df[col] = pd.to_numeric(volume_df[col], errors='coerce')

            if ticker not in volume_df.columns:
                print(f"Ticker {ticker} not found in volume data", file=sys.stderr)
                sys.exit(1)

            volume_data = volume_df[['Date', ticker]].copy()
            volume_data.columns = ['Date', 'Volume']

        # Filter training data using available date range
        train_data = df[(df['Date'] >= train_start_date) & (df['Date'] <= train_end_date)].copy()

        # Check minimum rows requirement
        if len(train_data) < 100:
            print(f"Insufficient training data: {len(train_data)} rows (minimum 100 required)", file=sys.stderr)
            print(f"Available training samples: {len(train_data)}", file=sys.stderr)
            sys.exit(1)

        # Get all data for predictions
        all_data = df[['Date', ticker]].copy()
        all_data.columns = ['Date', 'Adj Close']
        all_data = all_data.dropna().sort_values('Date')

        # Merge volume data if available
        if use_volume and volume_data is not None:
            volume_data = volume_data.dropna().sort_values('Date')
            all_data = pd.merge(all_data, volume_data, on='Date', how='inner')

            # Also prepare training data with volume
            train_volume = volume_data[
                (volume_data['Date'] >= train_start_date) &
                (volume_data['Date'] <= train_end_date)
            ].copy()
            train_data_with_volume = pd.merge(
                train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}),
                train_volume,
                on='Date',
                how='inner'
            )
            return all_data, train_data_with_volume, train_end_date.date()
        else:
            return all_data, train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}), train_end_date.date()

    except Exception as e:
        print(f"Error loading data: {e}", file=sys.stderr)
        sys.exit(1)


def create_sequences(df, feature_cols=None):
    """
    Create sequences for LSTM training with technical indicators

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with technical indicator features
    feature_cols : list, optional
        List of feature column names. If None, uses all columns except 'Date'

    Returns:
    --------
    tuple
        (X, y) where X has shape (samples, WINDOW_DAYS, num_features) and y is binary labels
    """
    if feature_cols is None:
        feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']

    X, y = [], []

    # Iterate from 0 to (len(df) - WINDOW_DAYS - HORIZON_DAYS)
    for i in range(len(df) - WINDOW_DAYS - HORIZON_DAYS):
        # Extract sequence from df.iloc[i:i+WINDOW_DAYS, feature_cols] with shape (75, 6)
        sequence = df.iloc[i:i+WINDOW_DAYS][feature_cols].values
        X.append(sequence)

        # Calculate target index: t = i + WINDOW_DAYS - 1, target_date = t + HORIZON_DAYS
        t = i + WINDOW_DAYS - 1
        target_date = t + HORIZON_DAYS

        # Create binary label: 1 if close[target_date] > close[t], else 0
        current_price = df.iloc[t][feature_cols[0]]  # Use first feature column (close)
        future_price = df.iloc[target_date][feature_cols[0]]  # Use first feature column (close)
        y.append(1 if future_price > current_price else 0)

    return np.array(X), np.array(y)


def focal_loss(gamma=2.0):
    """Focal Loss to address class imbalance more effectively than class weights"""
    def loss(y_true, y_pred):
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)
        p_t = y_true * y_pred + (1 - y_true) * (1 - y_pred)
        return tf.pow(1 - p_t, gamma) * bce
    return loss


def build_lstm_model(input_shape=(WINDOW_DAYS, 6)):
    """
    Build simplified LSTM classifier model

    Architecture: LSTM(32) → Dropout(0.2) → LSTM(32) → Dense(1, sigmoid)
    Loss: focal_loss(gamma=2.0) with no class_weight
    """
    model = Sequential([
        LSTM(32, return_sequences=True, input_shape=input_shape),
        Dropout(0.2),
        LSTM(32, return_sequences=False),
        Dense(1, activation='sigmoid')
    ])

    model.compile(
        optimizer='adam',
        loss=focal_loss(gamma=2.0),
        metrics=['binary_accuracy']
    )

    return model


def build_sequence(all_data, indicators_df, scaler, target_date):
    """
    Build a single sequence for prediction on target_date

    Parameters:
    -----------
    all_data : pandas.DataFrame
        DataFrame with Date and price columns (Date as Timestamp)
    indicators_df : pandas.DataFrame
        DataFrame with technical indicators
    scaler : sklearn.preprocessing.MinMaxScaler
        Fitted scaler for features
    target_date : date or Timestamp
        Target date for sequence building

    Returns:
    --------
    numpy.ndarray
        Scaled sequence of shape (1, 75, 6)
    """
    # Convert target_date to Timestamp for consistent comparison
    target_ts = pd.Timestamp(target_date)

    # Find target date in data
    date_mask = all_data['Date'] == target_ts
    if not date_mask.any():
        # Find nearest available date before target
        available_dates = all_data['Date'].values
        dates_before = [d for d in available_dates if d <= target_ts]
        if not dates_before:
            raise ValueError(f"No data available before {target_date}")
        actual_date = max(dates_before)
        date_mask = all_data['Date'] == actual_date

    date_idx = all_data[date_mask].index[0]

    # Check if we have enough data for WINDOW_DAYS sequence
    if date_idx < WINDOW_DAYS:
        raise ValueError(f"Insufficient data before {target_date}")

    # Extract most recent WINDOW_DAYS (75) days and apply scaler
    sequence_data = indicators_df.iloc[date_idx-WINDOW_DAYS:date_idx].values
    sequence_scaled = scaler.transform(sequence_data)
    return sequence_scaled.reshape(1, WINDOW_DAYS, 6)  # (1, 75, 6)


def get_actual_label_if_available(all_data, target_date):
    """
    Get actual label for target_date if data is available

    Returns None for future dates, 0/1 for historical dates
    """
    try:
        # Convert target_date to Timestamp for consistent comparison
        target_ts = pd.Timestamp(target_date)

        date_mask = all_data['Date'] == target_ts
        if not date_mask.any():
            return None

        date_idx = all_data[date_mask].index[0]

        # Check if we have future data for HORIZON_DAYS prediction
        if date_idx + HORIZON_DAYS - 1 >= len(all_data):
            return None

        # Calculate actual label
        price_col = 'Adj Close' if 'Adj Close' in all_data.columns else 'close'
        current_price = all_data.iloc[date_idx-1][price_col]
        future_price = all_data.iloc[date_idx + HORIZON_DAYS - 1][price_col]
        return 1 if future_price > current_price else 0

    except Exception:
        return None


def main():
    """
    Main function with new 6-day prediction approach

    Trains up to day -5, predicts 6 days (-4, -3, -2, -1, 0, +1)
    Uses bias offset only (no calibration/thresholds)
    """
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python lstm_service.py <TICKER> [--no-volume]", file=sys.stderr)
        sys.exit(1)

    ticker = sys.argv[1].upper()
    # Enable volume by default, disable with --no-volume flag
    use_volume = True
    if len(sys.argv) == 3 and sys.argv[2] == '--no-volume':
        use_volume = False

    try:
        # Load and prepare data with robust date handling
        all_data, train_data, _ = load_and_prepare_data(ticker, use_volume)

        # Calculate train_until (5 business days before first prediction date)
        train_until = pd.Timestamp(PRED_DATES[0]) - BDay(5)

        # Filter training data up to train_until
        train_data_filtered = train_data[train_data['Date'] <= train_until].copy()

        if len(train_data_filtered) < 100:
            print(f"Insufficient training data for {ticker}", file=sys.stderr)
            sys.exit(1)

        # Compute technical indicators on training data
        train_indicators = compute_indicators(train_data_filtered)

        print(f"학습 데이터 형태: {train_indicators.shape}", file=sys.stderr)

        # Scale the features
        scaler = MinMaxScaler(feature_range=(0, 1))
        train_scaled = scaler.fit_transform(train_indicators.values)

        # Create sequences with technical indicators
        X_train, y_train = create_sequences(pd.DataFrame(train_scaled, columns=train_indicators.columns))

        print(f"입력 형태: {X_train.shape}", file=sys.stderr)  # Should show (samples, 75, 6)
        print(f"학습 라벨 비율:", round(y_train.mean(), 3), file=sys.stderr)

        # Calculate bias offset
        bias = float(y_train.mean() - 0.5)
        print(f"편향 오프셋(bias):", round(bias, 3), file=sys.stderr)

        # Build simplified model
        model = build_lstm_model((WINDOW_DAYS, 6))

        # Early stopping callback
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=3,
            restore_best_weights=True,
            verbose=2
        )

        # Train model (no class weights, no manual validation split)
        model.fit(
            X_train, y_train,
            epochs=20,
            batch_size=32,
            validation_split=0.15,
            callbacks=[early_stopping],
            shuffle=False,  # Preserve time series order
            verbose=0
        )

        # Save model, scaler, and bias
        ROOT_DIR = Path(__file__).resolve().parents[1]
        model_dir = ROOT_DIR / "data" / "lstm_results"
        model_dir.mkdir(parents=True, exist_ok=True)

        model.save(model_dir / f"{ticker}_model_w75_h5_rsi_bb.keras")
        joblib.dump(scaler, model_dir / f"{ticker}_scaler_w75.pkl")

        # Save bias to JSON
        bias_data = {"bias": bias}
        with open(model_dir / f"{ticker}_bias.json", 'w') as f:
            json.dump(bias_data, f)

        # Compute technical indicators on all data for predictions
        all_indicators = compute_indicators(all_data)

        # Generate 6-day predictions (-4, -3, -2, -1, 0, +1)
        dates_to_predict = (
            pd.bdate_range(end=train_until, periods=HORIZON_DAYS, closed="left")
            .append(pd.bdate_range(start=train_until + BDay(1), periods=1))
        )

        predictions, y_true, y_pred = [], [], []

        for d in dates_to_predict:
            try:
                # Build sequence for prediction
                seq = build_sequence(all_data, all_indicators, scaler, d - BDay(1))

                # Get raw prediction and apply bias offset
                p_raw = float(model.predict(seq, verbose=0)[0][0])
                p_adj = np.clip(p_raw - bias, 0.0, 1.0)
                lbl = int(p_adj > 0.5)

                # Get actual label if available
                act = get_actual_label_if_available(all_data, d)
                if act is not None:
                    y_true.append(act)
                    y_pred.append(lbl)
                    update_history_csv(ticker, d, int(lbl == act))

                predictions.append({
                    "date": d.strftime("%Y-%m-%d"),
                    "pred_prob_up": round(p_adj, 4),
                    "pred_prob_down": round(1 - p_adj, 4),
                    "predicted_label": lbl,
                    "actual_label": act,
                    "prediction_horizon": 5
                })

            except Exception as e:
                print(f"Error predicting for {d}: {e}", file=sys.stderr)
                continue

        # Calculate metrics using first 5 days that have labels
        if y_true:
            metrics = {
                "accuracy": round(accuracy_score(y_true, y_pred), 3),
                "precision": round(precision_score(y_true, y_pred, zero_division=0), 3),
                "recall": round(recall_score(y_true, y_pred, zero_division=0), 3),
                "f1": round(f1_score(y_true, y_pred, zero_division=0), 3)
            }
        else:
            metrics = {k: None for k in ["accuracy", "precision", "recall", "f1"]}

        print(f"성능 지표:", metrics, file=sys.stderr)

        # ── Traffic light thresholds (editable):
        #   prob_up < 0.45            → RED
        #   0.45 ≤ prob_up ≤ 0.55     → YELLOW
        #   prob_up > 0.55            → GREEN
        prob_up_next = predictions[-1]["pred_prob_up"]  # day +1
        if prob_up_next < 0.45:
            tl_color = "red"
        elif prob_up_next > 0.55:
            tl_color = "green"
        else:
            tl_color = "yellow"

        print(f"신호등:", tl_color, file=sys.stderr)

        # Create result
        result = {
            "symbol": ticker,
            "train_until": train_until.date().strftime("%Y-%m-%d"),
            "predictions": predictions,
            "traffic_light": tl_color,
            "metrics": metrics
        }

        # Output result as JSON
        print(json.dumps(result))

    except Exception as e:
        print(f"Error in main execution: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()