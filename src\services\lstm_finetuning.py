#!/usr/bin/env python3
"""
Intel-Optimized LSTM Stock Prediction Service for Fine-tuning
6-feature input (adj close, volume, RSI-14, Bollinger %) with 75-day window / 5-day horizon
Trains up to day -5, predicts 6 days (-4, -3, -2, -1, 0, +1) with bias offset only
"""

import os
import sys
import json
import random
import re
import argparse
from datetime import date, timedelta, datetime
from pathlib import Path

# Intel optimization environment variables
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
os.environ['TF_ENABLE_BF16_CONVOLUTIONS'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# TensorFlow imports with Intel optimizations
try:
    import tensorflow as tf
    tf.get_logger().setLevel('ERROR')

    # Enable Intel optimizations
    tf.config.threading.set_intra_op_parallelism_threads(16)
    tf.config.threading.set_inter_op_parallelism_threads(16)

    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import (
        Input, LSTM, Dense, Dropout, Bidirectional, SpatialDropout1D, Activation, dot, Softmax, Flatten, LayerNormalization, GaussianNoise, Lambda
    )
    from tensorflow.keras.models import Model
    from tensorflow.keras.optimizers import AdamW
    from tensorflow.keras.callbacks import EarlyStopping

    print("✅  TensorFlow-Intel 2.18.0 — oneDNN enabled", file=sys.stderr)

except ImportError as e:
    print(f"TensorFlow import error: {e}", file=sys.stderr)
    sys.exit(1)

import numpy as np
import pandas as pd
from pandas.tseries.offsets import BDay
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_curve
from sklearn.isotonic import IsotonicRegression
from sklearn.linear_model import LogisticRegression
import joblib

# Common Constants for Technical Indicator-based LSTM Prediction
WINDOW_DAYS = 90        # LSTM input sequence length (90 trading days)
HORIZON_DAYS = 5        # Prediction horizon (5 business days ahead)
RSI_PERIOD = 14         # RSI calculation period
BB_PERIOD = 20          # Bollinger Bands period
BB_STD_MULT = 2         # Bollinger Bands standard deviation multiplier


def compute_indicators(df):
    """
    Compute technical indicators using original column names.
    The input DataFrame must have a DatetimeIndex and 'Adj Close', 'Volume' columns.
    """
    df = df.sort_index().copy()

    # Calculate RSI(14)
    delta = df['Adj Close'].diff()
    gain = delta.clip(lower=0)
    loss = -delta.clip(upper=0)
    avg_gain = gain.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    avg_loss = loss.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    df['rsi14'] = rsi / 100.0

    # Calculate Bollinger Bands
    sma = df['Adj Close'].rolling(window=BB_PERIOD).mean()
    std = df['Adj Close'].rolling(window=BB_PERIOD).std()
    bb_upper = sma + (BB_STD_MULT * std)
    bb_lower = sma - (BB_STD_MULT * std)
    df['bb_upper_pct'] = ((df['Adj Close'] - bb_upper) / bb_upper * 100).clip(lower=-100, upper=100)
    df['bb_lower_pct'] = ((df['Adj Close'] - bb_lower) / bb_lower * 100).clip(lower=-100, upper=100)
    df['bb_width_pct'] = ((bb_upper - bb_lower) / sma * 100).clip(lower=0, upper=100)

    # Define target variable
    df['target'] = (df['Adj Close'].shift(-HORIZON_DAYS) > df['Adj Close']).astype(int)
    
    return df.dropna()


def get_random_target_date():
    """Generate random target date between 2024-09-01 and 2025-05-15"""
    start_date = date(2024, 9, 1)
    end_date = date(2025, 5, 15)

    # Calculate the number of days between start and end
    days_between = (end_date - start_date).days

    # Generate random number of days to add to start date
    random_days = random.randint(0, days_between)

    # Return the random date
    return start_date + timedelta(days=random_days)


def load_sp500_tickers():
    """Load S&P 500 tickers from sp500_enriched_final.ts"""
    ROOT_DIR = Path(__file__).resolve().parents[1]
    ts_file = ROOT_DIR / "data" / "sp500_enriched_final.ts"

    if not ts_file.exists():
        print(f"Error: {ts_file} not found", file=sys.stderr)
        sys.exit(1)

    tickers = []
    company_names = {}

    try:
        with open(ts_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract ticker symbols and company names using regex
        pattern = r'"([A-Z]+)":\s*{\s*name:\s*"([^"]+)"'
        matches = re.findall(pattern, content)

        for ticker, name in matches:
            tickers.append(ticker)
            company_names[ticker] = name

        return tickers, company_names

    except Exception as e:
        print(f"Error loading tickers: {e}", file=sys.stderr)
        sys.exit(1)


def get_available_dates():
    """Load available dates from the CSV file"""
    ROOT_DIR = Path(__file__).resolve().parents[1]
    csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

    if not csv_path.exists():
        print(f"Error: {csv_path} not found", file=sys.stderr)
        sys.exit(1)

    try:
        import pandas as pd
        df = pd.read_csv(csv_path)
        df['Date'] = pd.to_datetime(df['Date'])

        # Filter dates between September 2024 and May 15, 2025
        start_date = pd.Timestamp(date(2024, 9, 1))
        end_date = pd.Timestamp(date(2025, 5, 15))

        available_dates = df[(df['Date'] >= start_date) & (df['Date'] <= end_date)]['Date'].dt.date.tolist()
        return available_dates

    except Exception as e:
        print(f"Error loading available dates: {e}", file=sys.stderr)
        sys.exit(1)


def get_random_date_and_ticker(count=1):
    """Generate random dates and tickers for fine-tuning"""
    # Load all available tickers
    all_tickers, company_names = load_sp500_tickers()

    # Exclude specified tickers
    excluded_tickers = {'SW', 'GEV', 'SOLV', 'VLTO', 'KVUE', 'GEHC', 'CEG'}
    available_tickers = [t for t in all_tickers if t not in excluded_tickers]

    # Load available dates from the dataset
    available_dates = get_available_dates()

    if not available_dates:
        print("No available dates found in the specified range", file=sys.stderr)
        sys.exit(1)

    # Generate random selections
    selections = []
    for _ in range(count):
        # Random date from available dates
        random_date = random.choice(available_dates)

        # Random ticker
        random_ticker = random.choice(available_tickers)

        selections.append({
            'ticker': random_ticker,
            'company_name': company_names[random_ticker],
            'date': random_date
        })

    return selections


def load_and_prepare_data(ticker, target_date):
    """
    Loads 3 years of price and volume data from wide-format CSVs, merges them, 
    sets a DatetimeIndex, and adjusts the target_date to a valid prior trading day.
    """
    try:
        ROOT_DIR = Path(__file__).resolve().parents[1]

        # 1. Load Price Data (wide format)
        price_path = ROOT_DIR / 'data' / 'sp500_adj_close_3y.csv'
        price_data = pd.read_csv(price_path, usecols=['Date', ticker], parse_dates=['Date'], index_col='Date')
        price_data.rename(columns={ticker: 'Adj Close'}, inplace=True)

        # 2. Load Volume Data (wide format)
        volume_path = ROOT_DIR / 'data' / 'sp500_volume_3y.csv'
        volume_data = pd.read_csv(volume_path, usecols=['Date', ticker], parse_dates=['Date'], index_col='Date')
        volume_data.rename(columns={ticker: 'Volume'}, inplace=True)

        # 3. Merge into a single DataFrame
        all_data = pd.merge(price_data, volume_data, on='Date', how='left').dropna()
        all_data.sort_index(inplace=True)

        if all_data.empty:
            raise ValueError(f"No data found for ticker {ticker} after merging price and volume.")

        # 4. Adjust target_date to the nearest previous business day if it's not a trading day
        target_date = pd.to_datetime(target_date)
        if target_date not in all_data.index:
            try:
                # Find the last valid index location at or before the target_date
                loc = all_data.index.get_loc(target_date, method='ffill')
                target_date = all_data.index[loc]
            except KeyError:
                raise ValueError(f"Target date {target_date.date()} is outside the available data range for {ticker}")

        # 5. Define and filter for the training period (3 years before the adjusted target date)
        end_train_date = target_date - BDay(1)
        start_train_date = end_train_date - pd.DateOffset(years=3)
        
        train_data = all_data.loc[start_train_date:end_train_date]
        if train_data.empty:
            raise ValueError(f"Not enough historical data for {ticker} ending on {end_train_date.date()}")

        return all_data, train_data, target_date.date()

    except FileNotFoundError as e:
        raise ValueError(f"A data file was not found: {e}")
    except KeyError:
        # This will catch cases where the ticker column doesn't exist in the CSV
        raise ValueError(f"Data for ticker '{ticker}' not found in source files.")


def create_sequences(df, feature_cols=['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']):
    """
    Create sequences for LSTM training with technical indicators.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with technical indicator features
    feature_cols : list, optional
        List of feature column names. If None, uses all columns except 'Date'

    Returns:
    --------
    tuple
        (X, y) where X has shape (samples, WINDOW_DAYS, num_features) and y is binary labels
    """
    if feature_cols is None:
        # Default features
        feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
        # Filter to columns that actually exist in the dataframe
        feature_cols = [col for col in feature_cols if col in df.columns]

    X, y = [], []

    for i in range(len(df) - WINDOW_DAYS - HORIZON_DAYS):
        # Extract sequence from df.iloc[i:i+WINDOW_DAYS, feature_cols]
        sequence = df.iloc[i:i+WINDOW_DAYS][feature_cols].values

        X.append(sequence)

        # Calculate target index: t = i + WINDOW_DAYS - 1, target_date = t + HORIZON_DAYS
        t = i + WINDOW_DAYS - 1
        target_date = t + HORIZON_DAYS

        # Create binary label: 1 if close[target_date] > close[t], else 0
        current_price = df.iloc[t][feature_cols[0]]  # Use first feature column (close)
        future_price = df.iloc[target_date][feature_cols[0]]  # Use first feature column (close)
        y.append(1 if future_price > current_price else 0)

    return np.array(X), np.array(y)


def focal_loss(gamma=2.0):
    """Focal Loss to address class imbalance (no class weights)"""
    def loss(y_true, y_pred):
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)
        p_t = y_true * y_pred + (1 - y_true) * (1 - y_pred)
        return tf.pow(1 - p_t, gamma) * bce
    return loss


def build_lstm_model(input_shape=(WINDOW_DAYS, 6)):
    """
    Build advanced LSTM classifier model based on the new specifications.

    Architecture: LayerNorm -> GaussianNoise -> Bi-LSTM -> SpatialDropout -> Attention -> Dense Head
    Optimizer: AdamW(3e-4, weight_decay=1e-4)
    """
    # Lightweight Attention Layer
    def lightweight_attention(inputs):
        # inputs shape: (batch_size, sequence_length, features)
        x = Dense(16, activation='tanh', name='attention_dense_1')(inputs)
        x = Dense(1, name='attention_dense_2')(x)
        attention_weights = Softmax(axis=1, name='attention_softmax')(x)
        # Weighted sum
        context_vector = Lambda(lambda t: tf.reduce_sum(t, axis=1), name='attention_sum')(attention_weights * inputs)
        return context_vector

    # Model Definition
    inp = Input(shape=input_shape, name='input_layer')
    x = LayerNormalization(name='layer_norm')(inp)
    x = GaussianNoise(0.05, name='gaussian_noise')(x)
    x = Bidirectional(LSTM(48, return_sequences=True), name='bidirectional_lstm')(x)
    x = SpatialDropout1D(0.15, name='spatial_dropout')(x)
    
    # Attention Mechanism
    attn_output = lightweight_attention(x)

    # Dense Head
    x = Dense(24, activation='relu', name='dense_24')(attn_output)
    output = Dense(1, activation='sigmoid', name='output_layer')(x)

    model = Model(inputs=inp, outputs=output)

    # Compile with AdamW optimizer
    optimizer = AdamW(learning_rate=3e-4, weight_decay=1e-4)
    model.compile(optimizer=optimizer, loss='binary_crossentropy', metrics=['accuracy'])

    return model


def update_history_csv(ticker, prediction_date, is_correct):
    """
    Update traffic light history CSV (keep last 5 rows)

    Parameters:
    -----------
    ticker : str
        Stock ticker symbol
    prediction_date : date or Timestamp
        Date of the prediction
    is_correct : int
        1 if prediction was correct, 0 if incorrect
    """
    # Create traffic_history directory if it doesn't exist
    ROOT_DIR = Path(__file__).resolve().parents[1]
    history_dir = ROOT_DIR / "data" / "traffic_history"
    history_dir.mkdir(parents=True, exist_ok=True)

    # Path to ticker's history file
    history_file = history_dir / f"{ticker}.csv"

    # Convert prediction_date to date object for consistency
    if hasattr(prediction_date, 'date'):
        prediction_date = prediction_date.date()

    # Load existing history or create new DataFrame
    if history_file.exists():
        try:
            history_df = pd.read_csv(history_file)
            history_df['date'] = pd.to_datetime(history_df['date']).dt.date
        except Exception as e:
            print(f"Error reading history file {history_file}: {e}", file=sys.stderr)
            history_df = pd.DataFrame(columns=['date', 'is_correct'])
    else:
        history_df = pd.DataFrame(columns=['date', 'is_correct'])

    # Add new record
    new_record = pd.DataFrame({
        'date': [prediction_date],
        'is_correct': [is_correct]
    })
    history_df = pd.concat([history_df, new_record], ignore_index=True)

    # Keep only last 5 rows
    history_df = history_df.tail(5)

    # Save updated history
    history_df.to_csv(history_file, index=False)


def get_actual_label_if_available(all_data, target_date):
    """
    Get actual label for target_date if data is available

    For target_date T, compare price at T vs price at T+HORIZON_DAYS
    Returns None for future dates, 0/1 for historical dates
    """
    try:
        # Convert target_date to Timestamp for consistent comparison
        target_ts = pd.Timestamp(target_date)

        # Find target date in data
        date_mask = all_data['Date'] == target_ts
        if not date_mask.any():
            # Try to find nearest available date
            available_dates = all_data['Date'].values
            dates_at_or_before = [d for d in available_dates if d <= target_ts]
            if not dates_at_or_before:
                return None
            actual_target = max(dates_at_or_before)
            date_mask = all_data['Date'] == actual_target

        date_idx = all_data[date_mask].index[0]

        # Calculate future date (T + HORIZON_DAYS business days)
        future_date = target_ts + BDay(HORIZON_DAYS)
        future_mask = all_data['Date'] == future_date

        if not future_mask.any():
            # Try to find nearest available future date
            future_dates = [d for d in all_data['Date'].values if d >= future_date]
            if not future_dates:
                return None
            actual_future = min(future_dates)
            future_mask = all_data['Date'] == actual_future

        if not future_mask.any():
            return None

        future_idx = all_data[future_mask].index[0]

        # Calculate actual label: 1 if future_price > current_price, else 0
        price_col = 'Adj Close' if 'Adj Close' in all_data.columns else 'close'
        current_price = all_data.iloc[date_idx][price_col]
        future_price = all_data.iloc[future_idx][price_col]

        return 1 if future_price > current_price else 0

    except Exception as e:
        print(f"Error getting actual label for {target_date}: {e}", file=sys.stderr)
        return None


def save_result_to_file(result, company_name=None, target_date=None, output_dir=None):
    """Save LSTM result to JSON file with proper naming convention"""
    # Set default output directory
    if output_dir is None:
        ROOT_DIR = Path(__file__).resolve().parents[2]
        output_dir = ROOT_DIR / "data" / "finetuning"

    # Ensure output directory exists
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Create filename based on company name and date
    if company_name and target_date:
        # Clean company name for filename
        clean_name = re.sub(r'[^\w\s-]', '', company_name).strip()
        clean_name = re.sub(r'[-\s]+', '_', clean_name)
        if isinstance(target_date, date):
            target_date_str = target_date.strftime('%Y-%m-%d')
        else:
            target_date_str = str(target_date)
        filename = f"{clean_name}_{target_date_str}.json"
    else:
        # Fallback to timestamp-based naming
        ts = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"lstm_result_{ts}.json"

    # Save to file
    file_path = output_dir / filename
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    return file_path


def get_youden_cutoff(y_true, y_pred_prob):
    """Calculate the optimal cut-off point using Youden's J-statistic."""
    fpr, tpr, thresholds = roc_curve(y_true, y_pred_prob)
    j_scores = tpr - fpr
    j_ordered = sorted(zip(j_scores, thresholds), key=lambda k: k[0], reverse=True)
    if not j_ordered:
        return 0.5
    return j_ordered[0][1]


def process_single_ticker(ticker, target_date, use_volume, save_to_file=False, company_name=None):
    """Main processing function for a single ticker, with robust data handling."""
    start_time = datetime.now()
    try:
        # 1. Load data, get company name, and get the adjusted target_date
        all_data, train_data, adjusted_target_date = load_and_prepare_data(ticker, target_date)
        company_name = company_name if company_name else ticker

        # 2. Feature Engineering - Use original, consistent column names
        feature_cols = ['Adj Close', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
        if use_volume:
            feature_cols.insert(1, 'Volume')

        all_indicators = compute_indicators(all_data.copy())
        train_indicators = all_indicators.loc[train_data.index]

        # 3. Data Scaling
        scaler = MinMaxScaler()
        train_scaled = scaler.fit_transform(train_indicators[feature_cols])
        train_scaled_df = pd.DataFrame(train_scaled, index=train_indicators.index, columns=feature_cols)

        # 4. Create Sequences
        X, y = create_sequences(train_scaled_df, feature_cols=feature_cols)
        if len(X) < 20:
            raise ValueError(f"Insufficient training data for {ticker} (found {len(X)} samples)")

        # Split data into training and validation (80/20 split)
        split_idx = int(len(X) * 0.8)
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]

        # 5. Build and Train Model
        model = build_lstm_model(input_shape=(WINDOW_DAYS, len(feature_cols)))
        model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=10,
            batch_size=64,
            callbacks=[EarlyStopping(monitor='val_loss', patience=3, restore_best_weights=True)],
            verbose=0
        )

        # 6. Prepare Prediction Sequence using index location for robustness
        all_indicators_scaled = scaler.transform(all_indicators[feature_cols])
        all_indicators_scaled_df = pd.DataFrame(all_indicators_scaled, index=all_indicators.index, columns=feature_cols)

        end_date_ts = pd.Timestamp(adjusted_target_date)
        try:
            end_idx_loc = all_indicators_scaled_df.index.get_loc(end_date_ts)
        except KeyError:
            raise ValueError(f"Adjusted target date {adjusted_target_date} not in scaled data for {ticker}")

        start_idx_loc = end_idx_loc - WINDOW_DAYS + 1
        if start_idx_loc < 0:
            raise ValueError(f"Not enough data for a {WINDOW_DAYS}-day sequence ending {adjusted_target_date}")

        pred_sequence_df = all_indicators_scaled_df.iloc[start_idx_loc:end_idx_loc + 1]

        if len(pred_sequence_df) != WINDOW_DAYS:
            raise ValueError(f"Could not retrieve {WINDOW_DAYS} days for prediction sequence ending {adjusted_target_date}. Found {len(pred_sequence_df)}.")

        X_pred = np.expand_dims(pred_sequence_df.values, axis=0)

        # 7. Probability Calibration
        y_val_pred_proba = model.predict(X_val, verbose=0).flatten()
        calibrated_proba, calibration_model = calibrate_probabilities(y_val, y_val_pred_proba)

        # 8. Final Prediction
        raw_prediction = model.predict(X_pred, verbose=0).flatten()[0]
        final_prediction_proba = raw_prediction

        if calibration_model:
            final_prediction_proba = calibration_model.predict([raw_prediction])[0]

        final_prediction_proba = np.clip(final_prediction_proba, 0, 1)

        # 9. Dynamic Cut-off and Alpha Blending
        recent_volatility = all_indicators['Adj Close'][-60:].pct_change().std() * np.sqrt(252)
        dynamic_cutoff, alpha = get_dynamic_cutoff(recent_volatility)
        final_prediction = 1 if final_prediction_proba >= dynamic_cutoff else 0

        # 10. Quality Gate
        history_df = load_prediction_history(ticker)
        recent_hits = history_df['hit'][-4:].tolist() if not history_df.empty else []
        quality_gate_alert = check_quality_gate(recent_hits)

        # 11. Calculate Metrics
        y_val_pred = (y_val_pred_proba >= dynamic_cutoff).astype(int)
        accuracy = accuracy_score(y_val, y_val_pred)
        f1 = f1_score(y_val, y_val_pred, zero_division=0)
        precision = precision_score(y_val, y_val_pred, zero_division=0)
        recall = recall_score(y_val, y_val_pred, zero_division=0)

        # 12. Get actual outcome and update history
        actual_label = get_actual_label_if_available(all_data, adjusted_target_date)
        is_correct = int(actual_label == final_prediction) if actual_label is not None else None
        
        if is_correct is not None:
            update_history_csv(ticker, adjusted_target_date, is_correct)

        # 13. Assemble final result
        end_time = datetime.now()
        runtime = (end_time - start_time).total_seconds()
        
        result = {
            "symbol": ticker,
            "company_name": company_name,
            "target_date": pd.to_datetime(adjusted_target_date).strftime("%Y-%m-%d"),
            "prediction": final_prediction,
            "actual_label": actual_label,
            "is_correct": is_correct,
            "calibrated_prob": float(final_prediction_proba),
            "cutoff": float(dynamic_cutoff),
            "alpha": float(alpha),
            "raw_prediction": float(raw_prediction),
            "accuracy_validation": float(accuracy),
            "f1_score_validation": float(f1),
            "precision_validation": float(precision),
            "recall_validation": float(recall),
            "runtime_seconds": round(runtime, 2),
            "quality_gate_alert": quality_gate_alert,
            "prediction_hits_last_4": recent_hits,
            "model_used": "BiLSTM_Attention",
            "calibration_method": str(type(calibration_model).__name__) if calibration_model else "None"
        }

        if save_to_file:
            save_result_to_file(result, company_name, adjusted_target_date)

        print(json.dumps(result, indent=2))

    except Exception as e:
        error_msg = f"Error processing {ticker} on {target_date}: {e}"
        print(error_msg, file=sys.stderr)
        return {"error": error_msg, "symbol": ticker}


def main():
    """Main function for LSTM fine-tuning service with batch processing support"""
    parser = argparse.ArgumentParser(
        description="LSTM Fine-tuning Service with Random Ticker and Date Selection",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python lstm_finetuning.py --count 10           # Generate 10 random samples
  python lstm_finetuning.py AAPL 2024-12-15      # Specific ticker and date
  python lstm_finetuning.py AAPL --random        # Specific ticker, random date
  python lstm_finetuning.py AAPL                 # Specific ticker, random date
        """
    )

    parser.add_argument(
        '--count', '-c',
        type=int,
        help='Number of random fine-tuning samples to generate'
    )

    parser.add_argument(
        'ticker',
        nargs='?',
        help='Ticker symbol (required unless using --count)'
    )

    parser.add_argument(
        'date',
        nargs='?',
        help='Target date (YYYY-MM-DD) or --random'
    )

    parser.add_argument(
        '--no-volume',
        action='store_true',
        help='Disable volume features (use price only)'
    )

    args = parser.parse_args()

    # Enable volume by default, disable if --no-volume flag is used
    use_volume = not args.no_volume

    if args.count:
        # Batch processing mode: generate multiple random samples
        print(f"[INFO] Generating {args.count} random LSTM fine-tuning samples...", file=sys.stderr)
        selections = get_random_date_and_ticker(args.count)

        success_count = 0
        for i, selection in enumerate(selections, 1):
            try:
                ticker = selection['ticker']
                company_name = selection['company_name']
                target_date = selection['date']

                print(f"[INFO] Processing {i}/{args.count}: {ticker} ({company_name}) on {target_date.strftime('%Y-%m-%d')}", file=sys.stderr)

                result = process_single_ticker(ticker, target_date, use_volume, save_to_file=True, company_name=company_name)
                if result:
                    success_count += 1

            except Exception as e:
                print(f"[ERROR] {selection['ticker']}: {e}", file=sys.stderr)

        print(f"[INFO] Completed! Successfully processed {success_count}/{args.count} samples", file=sys.stderr)

    else:
        # Single ticker mode
        if not args.ticker:
            parser.error("Ticker is required unless using --count mode")

        ticker = args.ticker.upper()

        # Determine target date
        if args.date:
            if args.date == '--random':
                target_date = get_random_target_date()
                print(f"Using random target date: {target_date}", file=sys.stderr)
            else:
                try:
                    target_date = date.fromisoformat(args.date)
                except ValueError:
                    print(f"Invalid date format: {args.date}. Use YYYY-MM-DD or --random", file=sys.stderr)
                    sys.exit(1)
        else:
            # Default to random date if no date specified
            target_date = get_random_target_date()
            print(f"Using random target date: {target_date}", file=sys.stderr)

        # Process single ticker and output to stdout
        result = process_single_ticker(ticker, target_date, use_volume, save_to_file=False)
        if result:
            print(json.dumps(result))
        else:
            sys.exit(1)


if __name__ == "__main__":
    main()
